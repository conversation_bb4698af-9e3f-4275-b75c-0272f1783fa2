"use client";

import {
  BoxIcon,
  ChartBarIcon,
  ChartPieIcon,
  HomeIcon,
  PanelLeftIcon,
} from "lucide-react";
import Image from "next/image";

import { NavMain, NavSettings } from "~/components/sidebar/sidebar-navigation";
import { NavUser } from "~/components/sidebar/sidebar-user";
import { Separator } from "~/components/ui/separator";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  useSidebar,
} from "~/components/ui/sidebar";
import { APP_NAME } from "~/lib/utils";

export function AppSidebar({
  superadmin,
  fullName,
  email,
  imageUrl,
}: {
  superadmin: boolean;
  fullName: string;
  email: string;
  imageUrl: string;
}) {
  const { state, toggleSidebar } = useSidebar();
  const isCollapsed = state === "collapsed";
  const navMainItems = [
    {
      title: "Home",
      url: "/",
      icon: HomeIcon,
    },
    {
      title: "Benchmarks",
      url: "/benchmarks",
      icon: BoxIcon,
    },
    {
      title: "Insights",
      url: "/insights",
      icon: ChartBarIcon,
    },
    {
      title: "Modeling",
      url: "/modeling",
      icon: ChartPieIcon,
    },
  ];

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              size="lg"
              onClick={toggleSidebar}
              className="flex justify-center"
            >
              {isCollapsed ? (
                <PanelLeftIcon className="size-4" />
              ) : (
                <div className="flex flex-1 items-center justify-between">
                  <Image
                    src="/logo.svg"
                    alt={APP_NAME}
                    width={135}
                    height={24}
                    className="mt-0.5 h-6 w-auto dark:invert dark:filter"
                  />
                  <PanelLeftIcon className="size-4" />
                </div>
              )}
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <Separator />
      <SidebarContent>
        <NavMain items={navMainItems} />
      </SidebarContent>
      <Separator />
      <SidebarContent className="flex-none">
        <NavSettings />
      </SidebarContent>
      <Separator />
      <SidebarFooter>
        <NavUser
          superadmin={superadmin}
          fullName={fullName}
          email={email}
          imageUrl={imageUrl}
        />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
